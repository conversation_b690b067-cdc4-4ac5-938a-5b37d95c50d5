import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
from services.scheduler_service.proposal_outline_scheduler_service import ProposalOutlineSchedulerService
from services.proposal.proposal_volumes_retrival import ProposalVolumeRetrievalService
from database import get_customer_db, CustomerSessionLocal
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends
from services.scheduler_service.proposal_scheduler_service import ProposalSchedulerService
from services.scheduler_service.custom_opps_scheduler_service import CustomOppsSchedulerService
from loguru import logger
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.outline import ProposalOutlineService
import ast
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from typing import Optional, Dict, List, Any
from controllers.customer.datametastore_controller import DataMetastoreController
from services.exports.generate_pdf_bytes import PDFGenerator as PDFGeneratorBytes
from services.exports.generate_docx_bytes import DocxBytesGenerator
from controllers.customer.rfp_draft_export_controller import RfpDraftExportController


toc_data = {
  "opportunity_id": "78620e4931944f38b5edf68ca026ed93",
  "volume_number": 1,
  "volume_title": "Volume I - Business",
  "toc_raw_content": "{\n  \"table_of_contents\": [\n    {\n      \"title\": \"Cover Letter\",\n      \"description\": \"This section provides a formal introduction to our organization and our proposal. It highlights our understanding of the RFP requirements and summarizes our key qualifications and capabilities. It also expresses our enthusiasm for the opportunity and our commitment to delivering exceptional results. This section directly addresses the evaluation criteria related to overall proposal quality and responsiveness.\",\n      \"number\": \"A\",\n      \"page_limit\": 2,\n      \"subsections\": []\n    },\n    {\n      \"title\": \"Organizational Conflict of Interest Mitigation Plan\",\n      \"description\": \"This section details our approach to identifying and mitigating any potential organizational conflicts of interest (OCI) that may arise during the performance of this contract. It outlines our OCI policies and procedures, identifies any existing or potential OCIs, and describes the specific mitigation strategies we will implement to ensure impartiality and objectivity. This section directly addresses the evaluation criteria related to organizational integrity and compliance.\",\n      \"number\": \"B\",\n      \"page_limit\": 2,\n      \"subsections\": []\n    },\n    {\n      \"title\": \"Copy of GSA Schedule\",\n      \"description\": \"This section includes a copy of our GSA Schedule, demonstrating our pre-approved pricing and terms for the services offered under this RFP. It provides the government with readily available information on our contract vehicles and pricing structure, streamlining the acquisition process. This section directly addresses the evaluation criteria related to contract compliance and pricing.\",\n      \"number\": \"C\",\n      \"page_limit\": 1,\n      \"subsections\": []\n    }\n  ]\n}",
  "table_of_contents": [
    {
      "title": "Cover Letter",
      "description": "This section provides a formal introduction to our organization and our proposal. It highlights our understanding of the RFP requirements and summarizes our key qualifications and capabilities. It also expresses our enthusiasm for the opportunity and our commitment to delivering exceptional results. This section directly addresses the evaluation criteria related to overall proposal quality and responsiveness.",
      "number": "A",
      "page_limit": 2,
      "subsections": []
    },
    {
      "title": "Organizational Conflict of Interest Mitigation Plan",
      "description": "This section details our approach to identifying and mitigating any potential organizational conflicts of interest (OCI) that may arise during the performance of this contract. It outlines our OCI policies and procedures, identifies any existing or potential OCIs, and describes the specific mitigation strategies we will implement to ensure impartiality and objectivity. This section directly addresses the evaluation criteria related to organizational integrity and compliance.",
      "number": "B",
      "page_limit": 2,
      "subsections": []
    },
    {
      "title": "Copy of GSA Schedule",
      "description": "This section includes a copy of our GSA Schedule, demonstrating our pre-approved pricing and terms for the services offered under this RFP. It provides the government with readily available information on our contract vehicles and pricing structure, streamlining the acquisition process. This section directly addresses the evaluation criteria related to contract compliance and pricing.",
      "number": "C",
      "page_limit": 1,
      "subsections": []
    }
  ],
  "section_count": 3,
  "generated_at": "2025-08-19T22:23:44.938803"
}

outline_data = {"outlines": [{"title": "Executive Summary", "page_limit": 2, "markdown": "## Executive Summary (Page limit: 2 pages)\n\n**Purpose:** To provide a concise overview of the proposal, highlighting key aspects of the proposed approach and demonstrating understanding of the solicitation requirements.\n\n**Required Information:**\n\n*   Demonstrate understanding of all major evaluation criteria.\n*   Highlight key aspects of the proposed technical approach.\n*   Highlight key aspects of the proposed management approach.\n*   Summarize relevant past performance.\n*   Acknowledge and respond to the extended solicitation close date. (\"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\")\n*   Acknowledge previous changes to the response date. (\"The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00 PM.\")\n\n**Suggested Structure & Headings:**\n\n*   **Technical Approach Summary**\n    *   Briefly describe the proposed solution.\n    *   Highlight key features and benefits.\n*   **Management Approach Summary**\n    *   Outline the proposed project management methodology.\n    *   Describe the team’s qualifications and experience.\n*   **Relevant Past Performance**\n    *   Summarize relevant projects and accomplishments.\n    *   Demonstrate experience aligned with solicitation requirements.\n*   **Solicitation Updates & Compliance**\n    *   Confirm understanding of the extended solicitation close date. (\"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\")\n    *   Acknowledge previous date changes. (\"The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00 PM.\")\n\n**Required Tables / Diagrams:**\n\n*   None specified in provided context.\n\n**References:**\n\n*   \"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\"\n*   \"The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00 PM.\"", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["5 reference snippet(s) not quoted verbatim in markdown References block."]}, {"title": "Technical Approach", "page_limit": 8, "markdown": "## Technical Approach (Page limit: 8 pages)\n\n**Purpose:** To detail the proposed technical solution and demonstrate fulfillment of all solicitation requirements.\n\n**Required Information:**\n\n*   Address all aspects of the Statement of Work (SOW) tasks, providing specific details on how each task will be accomplished.\n*   Demonstrate compliance with all technical specifications and standards.\n*   Include relevant diagrams, charts, and other visual aids to enhance clarity and understanding.\n*   Acknowledge the extended solicitation close date of July 28, 2025. (\"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\")\n\n**Suggested Structure & Headings:**\n\n*   **1. Overview of Proposed Solution**\n    *   High-level description of the technical approach.\n    *   Key technologies and methodologies to be employed.\n*   **2. Detailed Task Breakdown & Implementation**\n    *   For each task outlined in the Statement of Work (SOW):\n        *   Detailed description of the proposed implementation approach.\n        *   Specific technologies and tools to be utilized.\n        *   Deliverables for each task.\n        *   Timeline for completion.\n*   **3. Technical Compliance & Standards**\n    *   Demonstration of compliance with all technical specifications and standards outlined in the solicitation.\n    *   Mapping of proposed solution to specific requirements.\n*   **4. Visual Aids & Supporting Documentation**\n    *   Diagrams illustrating system architecture and data flow.\n    *   Charts depicting project timelines and resource allocation.\n    *   Supporting documentation (e.g., data sheets, specifications).\n*   **5. Risk Assessment & Mitigation**\n    *   Identification of potential technical risks.\n    *   Proposed mitigation strategies.\n\n**Required Tables / Diagrams:**\n\n*   **Task Breakdown Table:** A table listing each SOW task, the proposed implementation approach, deliverables, and timeline.\n*   **Compliance Matrix:** A matrix mapping each technical requirement to the corresponding aspect of the proposed solution.\n*   **System Architecture Diagram:** A visual representation of the proposed system architecture, including key components and data flow.\n*   **Project Timeline Chart:** A Gantt chart or similar visualization depicting the project timeline and key milestones.\n\n**References:**\n\n*   “Address all aspects of the Statement of Work (SOW) tasks, providing specific details on how each task will be accomplished.”\n*   “Demonstrate compliance with all technical specifications and standards.”\n*   “Include relevant diagrams, charts, and other visual aids to enhance clarity and understanding.”\n*   “The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.”\n*   OpportunitySAMTable [id=209922, title=1355-01-387-7738 T107 Suspension Band Set MK89 MOD1, description=https://api.sam.gov/prod/opportunities/v1/noticedesc?noticeid=78620e4931944f38b5edf68ca026ed93, postedDate=2025-06-25 00:00:00.0, archiveDate=2025-08-12 00:00:00.0, naicsCode=325920, naicsCodes=325920, typeOfSetAside=, typeOfSetAsideDescription=N/A, noticeId=78620e4931944f38b5edf68ca026ed93, solicitationNumber=N0010425RK010, fullParentPathName=DEPT OF DEFENSE.DEPT OF THE NAVY.NAVSUP.NAVSUP WEAPON SYSTEMS SUPPORT.NAVSUP WSS MECHANICSBURG.NAVSUP WEAPON SYSTEMS SUPPORT MECH, fullParentPathCode=017.1700.NAVSUP.NAVSUP WSS.NAVSUP WSS MECH.N00104, typeOp=Solicitation, baseTypeOp=Solicitation, archiveType=auto15, classificationCode=1355, pointOfContactName=Telephone: ************, pointOfContactEmail=<EMAIL>, pointOfContactPhone=N/A, placeOfPerformanceCityName=null, placeOfPerformanceStateName=null, placeOfPerformanceZip=null, placeOfPerformanceCountryName=null, ulink=https://sam.gov/opp/78620e4931944f38b5edf68ca026ed93/view, createdDate=2025-06-25 07:46:04.328, descriptionText=null, agencyCode=017, lastModDate=null, responseDeadLine=2025-07-28 14:00:00.0, pointOfContactFax=null, pointOfContactType=null, pointOfContactTitle=null, officeAddressZipcode=null, officeAddressCity=null, officeAddressCountryCode=null, officeAddressState=null, active=Yes, awardDate=null, awardNumber=null, awardAmount=null, awardAwardeeName=null, awardAwardeeLocationStreetAddress=null, awardAwardeeLocationCityCode=null, awardAwardeeLocationCityName=null, awardAwardeeLocationStateCode=null, awardAwardeeLocationStateName=null, awardAwardeeLocationZip=null, awardAwardeeLocationCountryCode=null, awardAwardeeLocationCountryName=null, awardAwardeeUeiSam=null, awardAwardeeDuns=null, awardAwardeeCageCode=null, additionalInfoLink=null, pointOfContactFullName=null, organizationType=null, placeOfPerformanceCityCode=null, placeOfPerformanceStateCode=null, placeOfPerformanceCountryCode=null, summaryText=null, requirementText=null, gradingCriteriaText=null, tocText=null, tocText2=null, tocText3=null, tocText4=null, tocText5=null, formatCompliance=null, keywords=null, status=null]", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["Forbidden token found in markdown: TO BE", "5 reference snippet(s) not quoted verbatim in markdown References block."]}, {"title": "Management Approach", "page_limit": 5, "markdown": "## Management Approach (Page limit: 5 pages)\n\n**Purpose:** To detail the project management plan, demonstrating a clear understanding of best practices and the ability to successfully manage the project.\n\n**Required Information:**\n\n*   Demonstrate understanding of project management best practices.\n*   Detail the organizational structure.\n*   Define roles and responsibilities.\n*   Outline communication strategies.\n*   Describe risk management procedures.\n*   Detail quality assurance processes.\n*   Address all relevant management requirements specified in the solicitation.\n\n**Suggested Structure & Headings:**\n\n*   **Project Organization (1 page)**\n    *   Organizational Chart: Visual representation of the project team structure.\n    *   Roles and Responsibilities: Detailed descriptions of each team member’s duties and authorities.\n*   **Communication Plan (1 page)**\n    *   Communication Matrix:  Details frequency, method, and audience for key project communications.\n    *   Reporting Procedures:  Describes how progress will be tracked and reported.\n*   **Risk Management (1.5 pages)**\n    *   Risk Identification:  List of potential risks and their likelihood/impact.\n    *   Mitigation Strategies:  Plans to reduce the probability or impact of identified risks.\n    *   Contingency Planning:  Actions to be taken if risks materialize.\n*   **Quality Assurance (1 page)**\n    *   Quality Control Measures:  Processes to ensure deliverables meet specified requirements.\n    *   Performance Metrics:  Key indicators used to track project performance.\n    *   Issue Resolution Process:  How issues will be identified, tracked, and resolved.\n*   **Project Schedule & Tracking (0.5 page)**\n    *   High-level project timeline.\n    *   Methods for tracking progress against the schedule.\n\n**Required Tables / Diagrams:**\n\n*   **Organizational Chart:**  A visual representation of the project team structure, showing reporting relationships.\n*   **Communication Matrix:** A table detailing communication methods, frequency, audience, and responsible parties.\n*   **Risk Register:** A table listing identified risks, their likelihood, impact, mitigation strategies, and contingency plans.\n\n**References:**\n\n*   “Describe the project management plan, including organizational structure, roles and responsibilities, communication strategies, risk management procedures, and quality assurance processes.”\n*   “This section should demonstrate a clear understanding of project management best practices and the ability to effectively manage the project to successful completion.”\n*   “Address all relevant management requirements specified in the solicitation.”", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["Forbidden token found in markdown: TO BE", "5 reference snippet(s) not quoted verbatim in markdown References block."]}, {"title": "Past Performance", "page_limit": 5, "markdown": "## Past Performance (Page limit: 5 pages)\n\n**Purpose:** To demonstrate the offeror’s capabilities and experience in successfully completing similar projects.\n\n**Required Information:**\n\n*   Description of three (3) relevant past projects.\n*   For each project:\n    *   Project description.\n    *   Offeror’s role.\n    *   Results achieved.\n    *   Challenges overcome.\n*   Quantifiable results.\n*   Client testimonials (highly encouraged).\n*   Demonstration of ability to meet the requirements of this solicitation.\n\n**Suggested Structure & Headings:**\n\n*   **Project 1: [Project Name]**\n    *   Project Overview: Detailed description of the project.\n    *   Offeror’s Role: Specific responsibilities and contributions.\n    *   Results Achieved: Quantifiable outcomes and benefits delivered.\n    *   Challenges Overcome: Description of obstacles and solutions implemented.\n*   **Project 2: [Project Name]**\n    *   Project Overview\n    *   Offeror’s Role\n    *   Results Achieved\n    *   Challenges Overcome\n*   **Project 3: [Project Name]**\n    *   Project Overview\n    *   Offeror’s Role\n    *   Results Achieved\n    *   Challenges Overcome\n\n**Required Tables / Diagrams:**\n\n*   None explicitly required. Tables summarizing project results or challenges could be beneficial but are not mandated.\n\n**References:**\n\n*   “Description of three (3) relevant past projects that demonstrate the offeror's capabilities and experience in successfully completing similar projects. For each project, include a description of the project, the offeror's role, the results achieved, and any challenges overcome.”\n*   “Quantifiable results and client testimonials are highly encouraged.”\n*   “This section should clearly demonstrate the offeror's ability to meet the requirements of this solicitation.”", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["5 reference snippet(s) not quoted verbatim in markdown References block."]}], "generation_summary": {"total_sections": 4, "produced": 4, "success_rate": 100.0}}
tenant_id = ""


extractor = ProposalOutlineSchedulerService()
retrieval = ProposalVolumeRetrievalService()
outline = ProposalSchedulerService()
compliance = CustomOppsSchedulerService()

structure_compliance_service = StructureComplianceService()
content_compliance_service = ContentComplianceService()
proposal_outline_service = ProposalOutlineService()

opportunity_id = toc_data["opportunity_id"]
volume_number = toc_data["volume_number"]
volume_toc = toc_data["table_of_contents"]

opportunity_id = "9657a510c28c48468f84e26ee8c6334b"

tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
client = "adeptengineeringsolutions"
source = "SAM"


async def generate_structure_compliance(opps_id, tenant_id, source):

  """Generate structure compliance and store in CustomOppsTable"""
  logger.info(f"Generating structure compliance for opportunity: {opps_id}")
  
  try:
      # Generate structure compliance using the service
      structure_result = await structure_compliance_service.generate_structure_compliance(
          opportunity_id=str(opps_id),
          tenant_id=str(tenant_id),
          source=str(source) or "custom",  # Default to "custom" if source is None
          max_tokens=2048
      )
      
      # Extract the generated content
      structure_data = structure_result.get("structured_data", {})
      structure_content_str = str(structure_data)
      update_fields = {
          "structure_compliance": structure_content_str
      }
      async for db in get_customer_db():
          updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
              db=db,
              opportunity_id=str(opportunity_id),
              update_fields=update_fields
          )
          break
      
      if updated_record:
          logger.info(f"Successfully stored structure compliance for opportunity: {opportunity_id}")
      else:
          logger.error(f"Failed to update CustomOppsTable for opportunity: {opportunity_id}")
      return structure_data
          
  except Exception as e:
      logger.error(f"Error generating structure compliance for opportunity {opps_id}: {e}")
      # Don't re-raise the exception to allow other processing steps to continue
  
  
async def generate_content_compliance(opps_id, tenant_id, source):
  """Generate content compliance and store in CustomOppsTable"""
  logger.info(f"Generating content compliance for opportunity: {opps_id}")
  
  try:
      
      content_result = await content_compliance_service.generate_content_compliance(
          opportunity_id=str(opps_id),
          tenant_id=str(tenant_id),
          source=str(source) or "custom",  # Default to "custom" if source is None
          is_rfp=True,
      )
      
      # Extract the generated content
      content_compliance_content = content_result.get("structured_data", {})

      content_compliance_str = str(content_compliance_content)
      
      # Update the CustomOppsTable with the content compliance
      update_fields = {
          "content_compliance": content_compliance_str
      }
      async for db in get_customer_db():
          updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
              db=db,
              opportunity_id=str(opportunity_id),
              update_fields=update_fields
          )
          break
      
      if updated_record:
          logger.info(f"Successfully stored content compliance for opportunity: {opportunity_id}")
      else:
          logger.error(f"Failed to update CustomOppsTable for opportunity: {opportunity_id}")
      
      return content_compliance_content
          
  except Exception as e:
      logger.error(f"Error generating content compliance for opportunity {opps_id}: {e}")
      # Don't re-raise the exception to allow other processing steps to continue


async def generate_table_of_contents(structure_compliance_str, content_compliance_str, opps_id, source):
  """Generate Table of Contents for each volume and store in CustomOppsTable"""
  logger.info(f"Generating Table of Contents for opportunity: {opps_id}")

  try:
      # Parse structure compliance JSON
      structure_compliance = ast.literal_eval(str(structure_compliance_str))
      content_compliance = ast.literal_eval(str(content_compliance_str))
      if not structure_compliance or "structure" not in structure_compliance:
          logger.warning(f"Invalid structure compliance JSON for opportunity: {opps_id}")
          return
      
      volume_definitions = structure_compliance["structure"]
      print(volume_definitions)

      # Split compliance data by volume
      volumes = {}

      # Extract structure data
      for volume in volume_definitions:
          volume_title = volume.get("volume_title", "")
          volumes[volume_title] = {
              "structure": volume,
              "content": None
          }

      # Match content compliance to volumes
      if content_compliance and "content_compliance" in content_compliance:
          logger.info("Using structured content compliance data...")
          for compliance in content_compliance["content_compliance"]:
              volume_title = compliance.get("volume_title", "")
              # Match volume titles
              for vol_key in volumes.keys():
                  if volume_title.lower() in vol_key.lower() or vol_key.lower() in volume_title.lower():
                      volumes[vol_key]["content"] = compliance
                      break

      else:
          logger.warning("No structured content compliance, will use full content for each volume")
          # Use full content compliance for each volume
          for vol_key in volumes.keys():
              volumes[vol_key]["content"] = {"content": content_compliance}

      logger.info(f"✓ Split compliance into {len(volumes)} volumes: {list(volumes.keys())}")

      update_fields = {}
      toc_fields = [
          "toc_text", "toc_text_2", "toc_text_3", "toc_text_4", "toc_text_5"
      ]

      print(f"VOLUMES: {volumes}")

      # Process each volume through the complete pipeline
      for volume_title, volume_data in volumes.items():
          logger.info(f"PROCESSING VOLUME: {volume_title}")

          # Prepare volume-specific data
          volume_structure = json.dumps({"structure": [volume_data["structure"]]}, indent=2)
          volume_content = json.dumps({"content_compliance": [volume_data["content"]]}, indent=2) if volume_data["content"] else "{}"

          # Generate TOC for this volume
          toc_result = await proposal_outline_service.generate_table_of_contents(
              opportunity_id=opportunity_id,
              tenant_id=tenant_id,
              source=source or "custom",
              volume_information=volume_structure,
              content_compliance=volume_content,
              is_rfp=True
          )
          print(f"TOC: {toc_result}")
          # Extract TOC JSON
          toc_data = None
          if toc_result and "content" in toc_result:
              content = toc_result["content"]
              try:
                  # Look for JSON in the content
                  import re
                  json_match = re.search(r'\{.*\}', content, re.DOTALL)
                  if json_match:
                      json_str = json_match.group()
                      toc_data = json.loads(json_str)
              except:
                  pass
          print("TOC_DATA: {toc_data}")
          if toc_data and "table_of_contents" in toc_data:
              volume_toc = toc_data["table_of_contents"]
              logger.info(f"✓ TOC generated successfully with {len(volume_toc)} sections")
          else:
              logger.error("⚠ TOC generation failed or no valid JSON found")
              volume_toc = []

          volume_number = list(volumes.keys()).index(volume_title)
          update_fields[toc_fields[volume_number]] = volume_toc

      if update_fields:
        async for db in get_customer_db():
            updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                db=db,
                opportunity_id=opportunity_id,
                update_fields=update_fields
            )
            break
        if updated_record:
            logger.info(f"Successfully stored Table of Contents for opportunity: {opportunity_id}")
        else:
            logger.error(f"Failed to update CustomOppsTable with TOC for opportunity: {opportunity_id}")

      return update_fields
      
  except Exception as e:
    logger.error(f"Error generating Table of Contents for opportunity {opps_id}: {e}")



async def extract_and_save_references(outline_result: dict, opportunity_id: str, tenant_id: str, volume_number: int = 1, volume_toc: Optional[list] = None):
    
    """Extract references from outline data and save to datametastore database as PDF bytes with TOC numbering"""
    try:
        if not outline_result or "outlines" not in outline_result:
            logger.info(f"No outline data found for opportunity {opportunity_id}, skipping references extraction")
            return

        toc_numbering = {}
        if volume_toc:
            for toc_item in volume_toc:
                title = toc_item.get("title", "")
                number = toc_item.get("number", "")
                if title and number:
                    toc_numbering[title] = number

        all_references = []
        for section in outline_result["outlines"]:
            title = section.get("title", "")
            references = section.get("references", [])

            # Get section number from TOC or use default
            section_number = toc_numbering.get(title, "")

            # Collect references for separate storage
            if references:
                section_refs = {
                    "section_number": section_number,
                    "section_title": title,
                    "references": references
                }
                all_references.append(section_refs)

        if not all_references:
            logger.info(f"No references found for opportunity {opportunity_id}")
            return

        references_content_lines = []
        references_content_lines.append(f"# References - Volume {volume_number}")
        references_content_lines.append("")
        
        for section_refs in all_references:
            section_number = section_refs.get("section_number", "")
            section_title = section_refs.get("section_title", "")
            references = section_refs.get("references", [])
            
            if references:
                header_title = f"{section_number} {section_title}" if section_number else section_title
                references_content_lines.append(f"## {header_title} - References")
                references_content_lines.append("")
                
                for i, reference in enumerate(references, 1):
                    cleaned_ref = reference.strip().replace('"', '').replace("'", "'")
                    references_content_lines.append(f"{i}. {cleaned_ref}")
                
                references_content_lines.append("")

        # Join all content into markdown
        references_markdown = "\n".join(references_content_lines)

        print(references_markdown)
        
        # Convert markdown to PDF bytes using PDFGenerator
        try:
            from services.exports.generate_pdf_bytes import PDFGenerator
            
            # Generate PDF bytes from the references markdown
            pdf_bytes, success_message = PDFGenerator.generate_pdf(
                markdown_content=references_markdown,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                cover_page_elements=None,
                toc_data=None,
                trailing_page_markdown=None,
                compliance=None,
                volume_number=volume_number,
                image_only=False
            )

            print(f"PDF_BYTES: {pdf_bytes}")
            
            logger.info(f"Successfully converted references to PDF: {success_message}")
            
        except ImportError as import_error:
            logger.error(f"Failed to import PDFGenerator: {import_error}")
        except Exception as pdf_error:
            logger.error(f"Failed to generate PDF for references: {pdf_error}")

        references_record_identifier = f"{opportunity_id}_references_vol_{volume_number}"
        
        # Save to datametastore
        async for db in get_customer_db():
            existing_record = await DataMetastoreController.get_by_record_identifier(
                db, references_record_identifier
            )
            
            if existing_record:
                await DataMetastoreController.update(
                    db=db,
                    record_id=existing_record.id,
                    original_document=pdf_bytes,
                    original_document_file_name=f"references_volume_{volume_number}_{opportunity_id}.pdf",
                    owner="SYSTEM"
                )
                logger.info(f"Updated existing references record for opportunity {opportunity_id}, volume {volume_number} with PDF")
            else:
                new_record = await DataMetastoreController.add(
                    db=db,
                    record_identifier=references_record_identifier,
                    record_type="PROPOSAL_REFERENCE",
                    tenant_id=tenant_id,
                    original_document_content_type="application/pdf",
                    original_document_file_name=f"references_volume_{volume_number}_{opportunity_id}.pdf",
                    original_document=pdf_bytes,
                    # raw_text_document=references_markdown,
                    owner="SYSTEM"
                )
                if new_record:
                    logger.info(f"✓ Saved references as PDF for opportunity {opportunity_id}, volume {volume_number} to datametastore (ID: {new_record.id})")
                else:
                    logger.error(f"Failed to save references PDF for opportunity {opportunity_id}, volume {volume_number}")
            break

    except Exception as e:
        logger.error(f"Error extracting and saving references as PDF for opportunity {opportunity_id}: {e}")
        import traceback
        traceback.print_exc()


async def generate_proposal_outline_custom(opportunity_id, tenant_id):
  """Generate proposal outline for each TOC and store in CustomOppsTable"""
  logger.info(f"Generating Proposal Outline for opportunity: {opportunity_id}")

  try:
      # Retrieve all TOCs from DB
      async for db in get_customer_db():
          record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
          break
      if not record:
          row = None
      else:
          row = (
              record.toc_text,
              record.toc_text_2,
              record.toc_text_3,
              record.toc_text_4,
              record.toc_text_5,
          )
      if not row:
          logger.warning(f"No TOC found for opportunity: {opportunity_id}")
          return

      toc_fields = ["toc_text", "toc_text_2", "toc_text_3", "toc_text_4", "toc_text_5"]
      outline_fields = [
          "proposal_outline_1", "proposal_outline_2", "proposal_outline_3",
          "proposal_outline_4", "proposal_outline_5"
      ]
      update_fields = {}

      for idx, toc_text in enumerate(row):
          if toc_text is None:
              continue
          try:
              toc = ast.literal_eval(str(toc_text))
          except Exception:
              logger.warning(f"TOC field {toc_fields[idx]} is not valid JSON for opportunity: {opportunity_id}")
              continue

          table_of_contents = toc
          if not table_of_contents:
              logger.warning(f"No table_of_contents in {toc_fields[idx]} for opportunity: {opportunity_id}")
              continue

          # Generate the outline using the ProposalOutlineService
          outline_result = await proposal_outline_service.generate_outline_markdown(
              opportunity_id=opportunity_id,
              tenant_id=tenant_id,
              source="custom",
              table_of_contents=table_of_contents,
              is_rfp=True
          )

          # Extract outline data
          outline_data = None
          if outline_result and "outlines" in outline_result:
              outline_data = outline_result["outlines"]
              logger.info(f"✓ Outline generated successfully with {len(outline_data)} sections for {toc_fields[idx]}")
              
              # Extract and save references immediately after outline generation
              volume_number = idx + 1
              await extract_and_save_references(
                  outline_result=outline_result,
                  opportunity_id=opportunity_id,
                  tenant_id=tenant_id,
                  volume_number=volume_number,
                  volume_toc=table_of_contents
              )
          else:
              logger.warning(f"⚠ Outline generation failed for {toc_fields[idx]} in opportunity: {opportunity_id}")
              continue

          # Store the outline result
          update_fields[outline_fields[idx]] = json.dumps(outline_result["outlines"], ensure_ascii=False)

      if update_fields:
          updated_record = None
          async for db in get_customer_db():
              updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                  db=db,
                  opportunity_id=opportunity_id,
                  update_fields=update_fields
              )
              break
          if updated_record:
              logger.info(f"Successfully stored Proposal Outline for opportunity: {opportunity_id}")
          else:
              logger.error(f"Failed to update CustomOppsTable with Proposal Outline for opportunity: {opportunity_id}")
      return update_fields
  except Exception as e:
      logger.error(f"Error generating Proposal Outline for opportunity {opportunity_id}: {e}")
      raise

pdf_bytes = b'%PDF-1.4\n%\x93\x8c\x8b\x9e ReportLab Generated PDF document http://www.reportlab.com\n1 0 obj\n<<\n/F1 2 0 R /F2 3 0 R /F3 4 0 R\n>>\nendobj\n2 0 obj\n<<\n/BaseFont /Helvetica /Encoding /WinAnsiEncoding /Name /F1 /Subtype /Type1 /Type /Font\n>>\nendobj\n3 0 obj\n<<\n/BaseFont /Times-Bold /Encoding /WinAnsiEncoding /Name /F2 /Subtype /Type1 /Type /Font\n>>\nendobj\n4 0 obj\n<<\n/BaseFont /Times-Roman /Encoding /WinAnsiEncoding /Name /F3 /Subtype /Type1 /Type /Font\n>>\nendobj\n5 0 obj\n<<\n/Contents 10 0 R /MediaBox [ 0 0 612 792 ] /Parent 9 0 R /Resources <<\n/Font 1 0 R /ProcSet [ /PDF /Text /ImageB /ImageC /ImageI ]\n>> /Rotate 0 /Trans <<\n\n>> \n  /Type /Page\n>>\nendobj\n6 0 obj\n<<\n/Contents 11 0 R /MediaBox [ 0 0 612 792 ] /Parent 9 0 R /Resources <<\n/Font 1 0 R /ProcSet [ /PDF /Text /ImageB /ImageC /ImageI ]\n>> /Rotate 0 /Trans <<\n\n>> \n  /Type /Page\n>>\nendobj\n7 0 obj\n<<\n/PageMode /UseNone /Pages 9 0 R /Type /Catalog\n>>\nendobj\n8 0 obj\n<<\n/Author (\\(anonymous\\)) /CreationDate (D:20250822021550+01\'00\') /Creator (\\(unspecified\\)) /Keywords () /ModDate (D:20250822021550+01\'00\') /Producer (ReportLab PDF Library - www.reportlab.com) \n  /Subject (\\(unspecified\\)) /Title (\\(anonymous\\)) /Trapped /False\n>>\nendobj\n9 0 obj\n<<\n/Count 2 /Kids [ 5 0 R 6 0 R ] /Type /Pages\n>>\nendobj\n10 0 obj\n<<\n/Filter [ /ASCII85Decode /FlateDecode ] /Length 1770\n>>\nstream\nGat=*gMZ"A&:Ml+eD"S9d\':hYD_-uUUi6W`\'!7JMW3I<*=kcZ^gL9[I>L`q01Gal"KS6!OFfN`d9DdZ+a*6E\\#4P+c=#W><!CU_mS#cqD/W?E^V[NNDZ4fL8e\\f;7k.*@_\\\\N"[a\\iA<%\'9YWT<&/Zfsk:C[Ep+YKn29IW3jVK"J6h5!n$IiE/1&8MVnAX\'VBY)f.rZf#):!8R1e:*p3V=l<q%/<1FNhS7s;Is=SJ3779=JC:[l^Q%S,",r<.$7$YT\\pn:S:_j4maB`)`nnJ5n:-UIRoq=9_&1"tIfLN[7KKH\'R;J"$BB)erP9,"QQg`X6]7ATq<W.MV"Gr.BRO!"g/$M4B?d+1q+Z[eI1Rc$a@:^^B811q#f(i>7"Sf8D7%m*:OO./3hZ7W0^`f/C:s._bn%RdV+II$]HB$8-<m,q_,0D*8&>FOeWSo0%(H8phC]%mgr9QVa;5)@uS)K8$hCW]Gs-,]lp=0Et/E`i[$pabDQ)/H7G-obUo2kPt=?%iLYrW;).3DINZVr6nHrQQkQIQ_d6d`1%K+V/Ze!+_@GmSRgrC<Q0!!:G*Wg!#6ZQ!<XOb>&m6R!@;g.,=/4led%s3YBt%tm/i7U>OeJcrO%TEW+o3_1D%q-l(nt\\6!38,[@UF9qEIp$+/P+nQri)KCcKPsR?\\#HD(4T94<dc6Ns/mbemsCe5G/lVu@)mj)@._g\\^$eJOfsG\'-bU/P$@PI6-_L+\'Wh@H=lqU6UXXbW[j_g!jBhpt*FGF"S^e(Fg+?JnbTA$!Nm&U@8b<,5HhK9R0ASA@G]Zm_4=@0QArj:.X:Bfii2e#*O8ZZq,@;[/n2d,/o8TJl_0Lp,U#.E1+,57(AU=:8\'\'QC?K1.h$$J$6#1=IljGTL.@-\\3NObV=6UmYQ2X%i7cP;<$s9\\HP\\Jubk7J63`8m4(CJ2V#>fOjD3,LI]UV\\fu`%,i__W!$R"]m.dC0>]X7PcI9fp/X%1p\\E6`3\\cSArBZH>Y#<O,u\\MbL`GbECB?(ZH7G^CMoG@kPcn>9"H6`L2Ccd#;qc]$KKcD0G$^lATH)c)"FJm9aJ0qR+BRcsb]YFXi8%FTbX9%#P#:Imdmu)u1ZULdJVZVX(I[mu1FT/$MnqU[jbj(ZKRL`8bJ%R;I+BK!=,ak%YB4f-PfN/dPsm$-4G7Kpm.l)i$sIk\'heV/79hEf+1ee`B>@#"6<ema-314tq>;CI]c4d;1%CWnc(lm_jb\\eR"CBXUTjDR.@iEqZsW]*gl1(H\\<(iKts]eUnSdkL(EhR]S9]jhs4qL`HDrR:EOf2%E$EONciBT&CDKEAoCn>rRgE\\&kV>&Ilr;lnp;:RDs!<19p;8*)W#M[ap%)RZ4RGg5Pd"*PQ@L=,<%3`s]+O(@pqn%O0u3;M97ZW$S82M69).+;XZR4$,sY?sZ7j0_l=B(Yt$)@Q:1_Wf$-*(S/_-<RG/F:p^min5;/a%6C+:qW3D%1JOfcNY,31Rkn^BLgDl,*[-7L,\\mRC\\F8mGiTf0D`/SuaB.&`c`U8Jq2Pn,,H\'/FlcT[B@Y#a-P9Tp\\G8p`Mc$P(c)r7)e[\\s\\Rs5t%7MrN6.E2&;"P]&6N8\'5Of7+J/D[QI9f@Hhtsa@D-6./i;-d:tVQZD;_R<N\\)WQl9/3-e^_p<+31uNQ53?"5tN,_/?;M6gW#GSeW,,N=SeglCMn+n=2Vj,1Zq*o*T/hH![tu4?SVEdL.>;aA%\\;5f7Ii2d;tAi;>,04P2^&>RXKX@GJF)-iNW3EO8f`46Qb_rrWnN[gW~>endstream\nendobj\n11 0 obj\n<<\n/Filter [ /ASCII85Decode /FlateDecode ] /Length 1025\n>>\nstream\nGasam95iiK&AI=/bb6HO_VpB<U.9)nCj->!\'A$8D.,YJ#O"E8QW0#N?lEBYIG.+G0bn^?`MA56#b_(O<&+AD]"LuS3_h%u_6FI+?YQ1sjQtI3g(]B1$EHdMKn@\\5_2pJ$Xk3#iFmu#(U#Z4,i8f3!-78kB]Qe-2WXo#=X8g:?qY-euJCe$Dpo*KNNW]IpC]u.]^i(cCKZQ5)Mq]2u73ik+>me,A4mkF5oT3]jVLS\'#/iYN6uqGS(q81\\=DHZ\')X+RJOjO=*-L%WWi[\\kl9aV(8L(Q=qi_?eH8od#c``LhasI)rU]/R@fUsJ3DpcO*NjE3-g!9KDnU)PpqZ$7S\\<hV,%TH:bkYpUu0jc[poWAPhPKFdA!1^bE[dV3$@S7Td`uaidXor!L7iTM/\'k@U>>tNJS"4Tl$FE[`il*r#eT1hlEY:U$IE.6D@s_C6\'tqD:4V3=0ip.!(+r+8i/[XROUs)UOiF@erJ<TKYT6*B>XPqp(Q7DB!-64KD]8A+[h&5dqVgj[^>;,$5D."%RXL^0iSeB)?[qKe0B%BUo>_8?l#6&9Cjs\'id=OQb@C[IJ*OWKS:2[64pT)/XX"65\'U,1PW_$\\FnRo33bk`=F[_o<"<-!$%h[<gVim"I@LO>]j8]$u]c)dnZR1obV?KB<P!M5@>@7N\'A.5X`D4El(GF;dYc)YMg(qK9]I?0iJ:QdtT!W1o.`]=kfEAQ\'0DuS28eg>pEl?6JWLD6p,$Sf`=uPMt&XoOrj"?aJ/<M<?sZ[#i\\)@fiNT$92DS)h(V>hj`7@h$:F(iEcoI>b$=8l4/iU^i4[VDe$i4FZQb:>K_NjKA#*[\'73-?WAIGqY&4R_uNr2g_h4PGk\\Q.\'+UNq$#KhZ@)#Z=f^JsNha!FJV-i]%<nmed"*:nnOjPU.5_c^]A-cYB.s+<2(D#iUPQ`\'PP\\_T6k0Lmp<[S;)%1T,eN961%I7WNjFh:n"`-Zp7^eV!LZTALU]7H:I2rQUV[(hh&)q5)$0\\:@i=`II2j-rnDe0p^:nK_lE~>endstream\nendobj\nxref\n0 12\n0000000000 65535 f \n0000000073 00000 n \n0000000124 00000 n \n0000000231 00000 n \n0000000339 00000 n \n0000000448 00000 n \n0000000642 00000 n \n0000000836 00000 n \n0000000904 00000 n \n0000001187 00000 n \n0000001252 00000 n \n0000003114 00000 n \ntrailer\n<<\n/ID \n[<12fbcc25ac404d9fe4c23e01c146269e><12fbcc25ac404d9fe4c23e01c146269e>]\n% ReportLab generated PDF document -- digest (http://www.reportlab.com)\n\n/Info 8 0 R\n/Root 7 0 R\n/Size 12\n>>\nstartxref\n4231\n%%EOF\n'


structure_compliance = str({'structure': [{'volume_title': 'Technical Capability', 'content': [{'section_name': 'Technical Capability', 'page_limit': 10}], 'total_page_limit': 10}, {'volume_title': 'Price', 'content': [{'section_name': 'Price', 'page_limit': 1}], 'total_page_limit': None}]})
content_compliance = str({'content_compliance': [{'volume_title': 'Volume I – Technical Capability', 'content': "This volume shall be clear, concise, and include sufficient detail for effective evaluation and substantiating claims. It must be legible, clear, and coherent.  The offer should not simply rephrase or restate the Government's requirements but provide convincing rationale demonstrating how the offeror intends to meet them. Unacceptable statements include those simply claiming understanding, compliance, or paraphrasing of the PWS, or using generic phrases like “standard procedures will be employed.”  Offerors must assume the Government has no prior knowledge of their facilities and experience. The evaluation will be based solely on the information presented in the offer. Content must be consistent with the PWS and evaluation criteria in 52.212-2 Addendum.  It should be formatted logically and with enough detail for a thorough evaluation of the contractor’s technical competence and ability to comply with the contract task requirements in the PWS.", 'page_limit': 10, 'evaluation_criteria': ['Technical Capability factors defined in 52.212-2 Addendum'], 'mandatory_sections': ['Response to PWS requirements (See attached Performance Work Statement)', 'Response to 52.212-2 Evaluation Factors (See attached Addendum 52.212-2)']}, {'volume_title': 'Volume II – Price', 'content': 'Complete Section B of the solicitation. The electronic version will take precedence for any discrepancies between hard copy and electronic versions. Certified cost or pricing data is not anticipated. Complete blocks 30a, b, and c. List Unit Pricing and Total Pricing for each Line Item (See attached Performance Work Statement). Complete the specified blocks on the SF 1449 related to Wage Determination (See attached Wage Determination), Addendum 52.212-1 Instructions to Offerors (See attached Addendum 52.212-1), and Addendum 52.212-2 Evaluation Factors (See attached Addendum 52.212-2).', 'page_limit': None, 'evaluation_criteria': [], 'mandatory_sections': None}]})

async def main():
    # await extractor._extract_and_save_references(
    #     outline_result=outline_data,
    #     opportunity_id=opportunity_id,
    #     tenant_id=tenant_id,
    #     volume_number=volume_number,
    #     volume_toc=volume_toc
    # )
    # async with CustomerSessionLocal() as db:
    #     await retrieval.get_all_volumes_from_review(
    #         db=db,
    #         tenant_id=tenant_id,
    #         opportunity_id=opportunity_id
    #     )

    # all_outlines = await outline._get_all_outlines(
    #     opportunity_id, tenant_id, "custom"
    # )

    # print(all_outlines)

    

    structure_compliance = await generate_structure_compliance(
        opportunity_id,
        tenant_id,
        "sam"
    )
    print("="*80)
    print(type(structure_compliance))
    print(structure_compliance)

    content_compliance = await generate_content_compliance(
        opportunity_id,
        tenant_id,
        "sam"
    )
    print("="*80)
    print(type(content_compliance))
    print(content_compliance)



    # table_of_content = await generate_table_of_contents(
    #     structure_compliance,
    #     content_compliance,
    #     opportunity_id,
    #     "custom"
    # )
    # print("="*80)
    # print(type(table_of_content))
    # print(table_of_content)


    # outlines = await generate_proposal_outline_custom(
    #     opportunity_id,
    #     tenant_id
    # )
    # print("="*80)
    # print(type(outlines))
    # print(outlines)


    # with open("output.pdf", "wb") as f:
    #     f.write(pdf_bytes)


    


if __name__ == "__main__":
    asyncio.run(main())
This service uses LLM to intelligently extract document titles and submission requirements
from solicitation data, providing meaningful names for generated proposal documents.